const fs = require('fs');
const path = require('path');

/**
 * Custom config loader that replaces the 'config' package
 * This module loads configuration from environment variables with fallbacks
 */

// Load .env file if it exists (for development)
const envPath = path.join(__dirname, '../../../.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
}

/**
 * Get environment variable with fallback value
 * @param {string} key - Environment variable key
 * @param {any} fallback - Fallback value if env var is not set
 * @returns {any} - Environment variable value or fallback
 */
function getEnvVar(key, fallback = null) {
  const value = process.env[key];
  
  // Return fallback if value is undefined, null, or empty string
  if (value === undefined || value === null || value === '') {
    return fallback;
  }
  
  // Convert string boolean values
  if (value === 'true') return true;
  if (value === 'false') return false;
  
  // Convert string numbers
  if (!isNaN(value) && !isNaN(parseFloat(value))) {
    return parseFloat(value);
  }
  
  return value;
}

/**
 * Configuration object with environment variables and fallbacks
 */
const config = {
  // Database Configuration
  redis: {
    connections: {
      master: {
        host: getEnvVar('REDIS_HOST', 'localhost'),
        port: getEnvVar('REDIS_PORT', 6379),
        database: getEnvVar('REDIS_DATABASE', 0),
        password: getEnvVar('REDIS_PASSWORD', null)
      }
    }
  },
  
  mongo: {
    connections: {
      master: {
        host: getEnvVar('MONGO_HOST', 'localhost'),
        port: getEnvVar('MONGO_PORT', 27017),
        database: getEnvVar('MONGO_DATABASE', 'visiobox'),
        options: {
          useUnifiedTopology: true,
          useNewUrlParser: true,
          user: getEnvVar('MONGO_USER', null),
          pass: getEnvVar('MONGO_PASSWORD', null)
        }
      }
    }
  },
  
  // Application Configuration
  port: getEnvVar('PORT', 3001),
  logLevel: getEnvVar('LOG_LEVEL', 'info'),
  secretKey: getEnvVar('SECRET_KEY', '4NNUmWbLFStDIu+AjpaFtzTB5kDWMSCuf0OK0iIOjIc='),
  serviceName: getEnvVar('SERVICE_NAME', 'VISIOBOX-BACKEND'),
  currentUrl: getEnvVar('CURRENT_URL', 'http://localhost:3001'),
  
  // Telegram Configuration
  telegram: {
    botToken: getEnvVar('TELEGRAM_BOT_TOKEN', '**********************************************'),
    chatId: getEnvVar('TELEGRAM_CHAT_ID', '5896852819'),
    adminChatId: getEnvVar('TELEGRAM_ADMIN_CHAT_ID', '5896852819')
  },
  
  // OAuth Configuration
  oauth: {
    enabled: getEnvVar('OAUTH_ENABLED', false),
    google: {
      enabled: getEnvVar('OAUTH_GOOGLE_ENABLED', false),
      clientId: getEnvVar('OAUTH_GOOGLE_CLIENT_ID', 'your_google_client_id'),
      clientSecret: getEnvVar('OAUTH_GOOGLE_CLIENT_SECRET', 'your_google_client_secret'),
      callbackURL: getEnvVar('OAUTH_GOOGLE_CALLBACK_URL', 'http://localhost:3001/auth/google/callback')
    },
    telegram: {
      enabled: getEnvVar('OAUTH_TELEGRAM_ENABLED', false),
      botToken: getEnvVar('OAUTH_TELEGRAM_BOT_TOKEN', '**********************************************'),
      callbackURL: getEnvVar('OAUTH_TELEGRAM_CALLBACK_URL', 'http://localhost:3001/auth/telegram/callback')
    }
  },
  
  // Session Configuration
  session: {
    secret: getEnvVar('SESSION_SECRET', '4NNUmWbLFStDIu+AjpaFtzTB5kDWMSCuf0OK0iIOjIc='),
    maxAge: getEnvVar('SESSION_MAX_AGE', 86400000)
  },
  
  // Email Configuration
  emailInfos: [
    {
      service: getEnvVar('EMAIL_SERVICE_1', 'gmail'),
      auth: {
        user: getEnvVar('EMAIL_USER_1', '<EMAIL>'),
        pass: getEnvVar('EMAIL_PASS_1', 'pnoimjonjkwtvbxp')
      }
    },
    {
      service: getEnvVar('EMAIL_SERVICE_2', 'gmail'),
      auth: {
        user: getEnvVar('EMAIL_USER_2', '<EMAIL>'),
        pass: getEnvVar('EMAIL_PASS_2', 'uoxozzqkqbzehbvf')
      }
    },
    {
      service: getEnvVar('EMAIL_SERVICE_3', 'gmail'),
      auth: {
        user: getEnvVar('EMAIL_USER_3', '<EMAIL>'),
        pass: getEnvVar('EMAIL_PASS_3', 'qngntsssrviwwwtz')
      }
    },
    {
      service: getEnvVar('EMAIL_SERVICE_4', 'gmail'),
      auth: {
        user: getEnvVar('EMAIL_USER_4', '<EMAIL>'),
        pass: getEnvVar('EMAIL_PASS_4', 'ygoljpqkjlbpxexw')
      }
    },
    {
      service: getEnvVar('EMAIL_SERVICE_5', 'gmail'),
      auth: {
        user: getEnvVar('EMAIL_USER_5', '<EMAIL>'),
        pass: getEnvVar('EMAIL_PASS_5', 'jrusfbpoocqvzdft')
      }
    }
  ],
  
  // Email Alert Configuration
  listEmailAlert: [getEnvVar('EMAIL_ALERT_1', '<EMAIL>')]
};

/**
 * Get configuration value using dot notation
 * @param {string} path - Configuration path (e.g., 'mongo.connections.master.host')
 * @returns {any} - Configuration value
 */
function get(path) {
  return path.split('.').reduce((obj, key) => {
    return obj && obj[key] !== undefined ? obj[key] : null;
  }, config);
}

/**
 * Check if configuration has a specific path
 * @param {string} path - Configuration path
 * @returns {boolean} - True if path exists
 */
function has(path) {
  return get(path) !== null;
}

// Export config object with get and has methods to mimic the 'config' package
module.exports = {
  get,
  has,
  ...config
};
