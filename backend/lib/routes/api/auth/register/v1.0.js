const _ = require('lodash');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');

module.exports = async (req, res) => {
  try {
    const { username, password, email, name, phone } = req.body;

    // Check params
    if (!username.trim() || !password.trim() || !email.trim() || !name.trim()) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Password validation
    if (password.trim().length < 6) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Password must be at least 6 characters long'
      });
    }

    // Check existing user
    const existingUser = await UserModel.findOne({
      $or: [
        { username: username.trim().toLowerCase() },
        { email: email.trim().toLowerCase() }
      ]
    }).lean();

    if (existingUser) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.EXISTS
      });
    }

    // Create user
    const hashedPassword = await bcrypt.hash(password.trim(), 10);

    // Generate email verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationTokenExpiry = new Date(Date.now() + 86400000); // 24 hours

    const newUser = await UserModel.create({
      username: username.trim().toLowerCase(),
      password: hashedPassword,
      email: email.trim().toLowerCase(),
      name: name.trim(),
      phone: phone.trim(),
      role: 'user',
      status: 1,
      provider: 'local',
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationTokenExpiry,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Send verification email if email service is configured
    try {
      const emailConfig = config.get('emailInfos')[0];
      if (emailConfig && emailConfig.auth && emailConfig.auth.user) {
        const transporter = nodemailer.createTransport({
          service: emailConfig.service,
          auth: emailConfig.auth
        });

        const verificationUrl = `${process.env.REACT_APP_API_URL || config.get('currentUrl') || 'http://localhost:3001'}/verify-email?token=${verificationToken}`;

        const mailOptions = {
          from: emailConfig.auth.user,
          to: email,
          subject: 'Email Verification - VisioBox',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">Welcome to VisioBox!</h2>
              <p>Thank you for registering with VisioBox. Please verify your email address to complete your registration.</p>
              <p>Click the link below to verify your email:</p>
              <a href="${verificationUrl}" style="display: inline-block; padding: 12px 24px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 16px 0;">Verify Email</a>
              <p>This link will expire in 24 hours.</p>
              <p>If you didn't create this account, please ignore this email.</p>
              <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">VisioBox - Advanced file storage with visual preview</p>
            </div>
          `
        };

        await transporter.sendMail(mailOptions);
        console.log('Verification email sent to:', email);
      } else {
        console.log('Email service not configured, verification token generated but not sent');
      }
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail registration if email fails
    }

    // Send response
    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Registration successful! Please check your email to verify your account.',
      data: {
        id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        name: newUser.name,
        emailVerificationRequired: true
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
