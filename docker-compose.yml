version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      # Database Configuration
      - MONGODB_URI=mongodb://mongodb:27017/visiobox
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - MONGO_DATABASE=visiobox
      - MONGO_USER=
      - MONGO_PASSWORD=

      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DATABASE=0
      - REDIS_PASSWORD=

      # Telegram Configuration
      - TELEGRAM_BOT_TOKEN=**********************************************
      - TELEGRAM_CHAT_ID=5896852819
      - TELEGRAM_ADMIN_CHAT_ID=5896852819

      # Application Configuration
      - NODE_ENV=production
      - PORT=3000
      - SECRET_KEY=4NNUmWbLFStDIu+AjpaFtzTB5kDWMSCuf0OK0iIOjIc=
      - LOG_LEVEL=info
      - SERVICE_NAME=VISIOBOX-BACKEND
      - CURRENT_URL=http://localhost:3000

      # OAuth Configuration
      - OAUTH_ENABLED=false
      - OAUTH_GOOGLE_ENABLED=false
      - OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
      - OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
      - OAUTH_GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
      - OAUTH_TELEGRAM_ENABLED=false
      - OAUTH_TELEGRAM_BOT_TOKEN=**********************************************
      - OAUTH_TELEGRAM_CALLBACK_URL=http://localhost:3000/auth/telegram/callback

      # Session Configuration
      - SESSION_SECRET=4NNUmWbLFStDIu+AjpaFtzTB5kDWMSCuf0OK0iIOjIc=
      - SESSION_MAX_AGE=86400000

      # Email Configuration
      - EMAIL_SERVICE_1=gmail
      - EMAIL_USER_1=<EMAIL>
      - EMAIL_PASS_1=pnoimjonjkwtvbxp
      - EMAIL_SERVICE_2=gmail
      - EMAIL_USER_2=<EMAIL>
      - EMAIL_PASS_2=uoxozzqkqbzehbvf
      - EMAIL_SERVICE_3=gmail
      - EMAIL_USER_3=<EMAIL>
      - EMAIL_PASS_3=qngntsssrviwwwtz
      - EMAIL_SERVICE_4=gmail
      - EMAIL_USER_4=<EMAIL>
      - EMAIL_PASS_4=ygoljpqkjlbpxexw
      - EMAIL_SERVICE_5=gmail
      - EMAIL_USER_5=<EMAIL>
      - EMAIL_PASS_5=jrusfbpoocqvzdft

      # Email Alert Configuration
      - EMAIL_ALERT_1=<EMAIL>
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/public/uploads:/app/public/uploads
    networks:
      - app_network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
    depends_on:
      - backend
    networks:
      - app_network
    restart: unless-stopped

  mongodb:
    image: mongo:7.0
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=visiobox
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    networks:
      - app_network
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  app_network:
    driver: bridge
