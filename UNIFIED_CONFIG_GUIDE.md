# 🔧 Unified Configuration System

## Tổng quan

Hệ thống đã được cập nhật để sử dụng **một file config duy nhất** cho cả development và production thông qua environment variables.

## Những thay đổi đã thực hiện

### 1. Tạo Custom Config Loader
- **File**: `backend/lib/config/index.js`
- **Chức năng**: Thay thế package `config` bằng custom loader
- **Tính năng**: 
  - Load environment variables từ file `.env` (development)
  - Fallback values cho development
  - Tương thích với API của package `config` (get, has methods)

### 2. Cập nhật Environment Variables
- **File development**: `.env`
- **File production**: `docker-compose.yml`
- **<PERSON><PERSON> gồm**: Tất cả config cần thiết (database, telegram, oauth, email, etc.)

### 3. Cậ<PERSON> nhật tất cả imports
- <PERSON><PERSON> đổi `require('config')` thành đường dẫn tương đối đến custom config
- Cập nhật 15+ files trong backend

### 4. Cài đặt dependencies
- Thêm package `dotenv` để load file `.env`

## Cách sử dụng

### Development
```bash
# 1. Chỉnh sửa file .env với config của bạn
cp .env.example .env
nano .env

# 2. Start backend như bình thường
cd backend
npm start
```

### Production (Docker)
```bash
# 1. Environment variables đã được định nghĩa trong docker-compose.yml
# 2. Start với Docker như bình thường
docker-compose up -d
```

## Cấu trúc Config

### Environment Variables chính:
```env
# Database
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=visiobox
REDIS_HOST=localhost
REDIS_PORT=6379

# Application
PORT=3001
SECRET_KEY=your_secret_key
SERVICE_NAME=VISIOBOX-BACKEND

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

## Lợi ích

✅ **Một file config duy nhất** - Không cần maintain nhiều file config khác nhau

✅ **Environment-based** - Tự động switch giữa dev/prod dựa trên environment variables

✅ **Fallback values** - Development có default values, production override qua env vars

✅ **Docker-ready** - Production config được định nghĩa trong docker-compose.yml

✅ **Backward compatible** - Vẫn sử dụng `config.get()` như cũ

## Test

Backend đã được test thành công:
- ✅ Config loading hoạt động
- ✅ Environment variables được load đúng
- ✅ Database connections OK
- ✅ Telegram service initialized
- ✅ All routes registered

## Files đã thay đổi

### Core files:
- `backend/lib/config/index.js` (NEW)
- `backend/index.js`
- `.env`
- `.env.example`
- `docker-compose.yml`
- `README.md`

### Updated imports (15+ files):
- `backend/lib/connections/mongo/index.js`
- `backend/lib/connections/redis/index.js`
- `backend/lib/services/telegram.js`
- `backend/lib/logger/index.js`
- `backend/lib/middleware/verifyPermission.js`
- `backend/lib/util/location.js`
- All auth route files
- All other route files

## Kết luận

🎉 **Hoàn thành!** Bây giờ bạn có thể:
- Dùng `npm start` cho development với config từ `.env`
- Dùng `docker-compose up` cho production với config từ environment variables
- Chỉ cần maintain một bộ config duy nhất
- Dễ dàng deploy và scale
