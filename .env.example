# Database Configuration
MONGODB_URI=mongodb://localhost:27017/visiobox
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=visiobox
MONGO_USER=
MONGO_PASSWORD=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
TELEGRAM_ADMIN_CHAT_ID=your_telegram_chat_id_here

# Application Configuration
NODE_ENV=development
PORT=3001
SECRET_KEY=your_jwt_secret_key_here
LOG_LEVEL=info
SERVICE_NAME=VISIOBOX-BACKEND

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001
CURRENT_URL=http://localhost:3001

# OAuth Configuration
OAUTH_ENABLED=false
OAUTH_GOOGLE_ENABLED=false
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH_GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback
OAUTH_TELEGRAM_ENABLED=false
OAUTH_TELEGRAM_BOT_TOKEN=your_telegram_bot_token_for_login
OAUTH_TELEGRAM_CALLBACK_URL=http://localhost:3001/auth/telegram/callback

# Session Configuration
SESSION_SECRET=your_session_secret_key
SESSION_MAX_AGE=86400000

# Email Configuration
EMAIL_SERVICE_1=gmail
EMAIL_USER_1=<EMAIL>
EMAIL_PASS_1=your_app_password
EMAIL_SERVICE_2=gmail
EMAIL_USER_2=
EMAIL_PASS_2=
EMAIL_SERVICE_3=gmail
EMAIL_USER_3=
EMAIL_PASS_3=
EMAIL_SERVICE_4=gmail
EMAIL_USER_4=
EMAIL_PASS_4=
EMAIL_SERVICE_5=gmail
EMAIL_USER_5=
EMAIL_PASS_5=

# Email Alert Configuration
EMAIL_ALERT_1=<EMAIL>
