# VisioBox - Advanced File Storage with Visual Preview

VisioBox là một hệ thống quản lý file tiên tiến với khả năng xem trước trực quan, sử dụng Telegram làm kho lưu trữ đám mây miễn phí. Hệ thống cung cấp giao diện web hiện đại để quản lý file và thư mục với tính năng preview mạnh mẽ, tương tự như Google Drive/Dropbox.

## 🚀 Tính năng chính

- **Lưu trữ miễn phí**: Sử dụng Telegram Bot API để lưu trữ file không giới hạn
- **Giao diện trực quan**: Dashboard web responsive với Material-UI
- **Quản lý thư mục**: T<PERSON><PERSON>, đổi tên, xóa thư mục và file
- **Tìm kiếm nhanh**: Tìm kiếm file và thư mục theo tên
- **Upload đa file**: Hỗ trợ upload nhiều file cùng lúc với progress bar
- **Preview file**: Xem trước ảnh và video
- **Caching thông minh**: Sử dụng Redis để tối ưu hiệu suất

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Telegram      │
│   (React)       │◄──►│   (Node.js)     │◄──►│   Bot API       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │    MongoDB      │    │     Redis       │
                    │   (Database)    │    │   (Cache)       │
                    └─────────────────┘    └─────────────────┘
```

## 📋 Yêu cầu hệ thống

- Node.js 18+
- Docker & Docker Compose
- Telegram Bot Token
- MongoDB 7.0+
- Redis 7.2+

## 🛠️ Cài đặt và chạy

### 1. Clone repository

```bash
git clone <repository-url>
cd visiobox
```

### 2. Cấu hình môi trường

```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin của bạn:

```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/visiobox
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Configuration
NODE_ENV=development
PORT=3000
SECRET_KEY=your_jwt_secret_key_here

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3000
```

### 3. Tạo Telegram Bot

1. Tìm @BotFather trên Telegram
2. Gửi `/newbot` và làm theo hướng dẫn
3. Lưu Bot Token vào file `.env`
4. Tạo một chat/channel và lấy Chat ID

### 4. Chạy với Docker Compose

```bash
# Build và chạy tất cả services
docker-compose up -d

# Xem logs
docker-compose logs -f

# Dừng services
docker-compose down
```

### 5. Chạy development mode

```bash
# Backend
cd backend
npm install
npm run dev

# Frontend (terminal mới)
cd frontend
npm install
npm start
```

## 📁 Cấu trúc dự án

```
visiobox/
├── backend/                 # Node.js API Server
│   ├── lib/
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── middleware/     # Express middleware
│   │   └── connections/    # Database connections
│   ├── config/             # Configuration files
│   └── Dockerfile
├── frontend/               # React Dashboard
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API services
│   │   ├── contexts/       # React contexts
│   │   ├── types/          # TypeScript types
│   │   └── utils/          # Utility functions
│   └── Dockerfile
├── docker-compose.yml      # Docker orchestration
└── README.md
```

## 🔌 API Endpoints

### Files
- `POST /api/v1.0/files/upload` - Upload file
- `GET /api/v1.0/files/download/:fileId` - Download file

### Folders
- `POST /api/v1.0/folders` - Create folder
- `GET /api/v1.0/browse/:folderId?` - Browse folder content

### Items Management
- `PUT /api/v1.0/items/:id/rename` - Rename file/folder
- `DELETE /api/v1.0/items/:id` - Delete file/folder

### Search
- `GET /api/v1.0/search?q=query` - Search files/folders

## 🎯 Sử dụng

1. Truy cập http://localhost (hoặc http://localhost:3000 cho dev mode)
2. Upload ảnh/video bằng cách click nút "Upload" hoặc FAB
3. Tạo thư mục mới bằng FAB màu đỏ
4. Tìm kiếm file bằng thanh search ở header
5. Click chuột phải vào file/folder để xem menu context
6. Navigate qua các thư mục bằng breadcrumbs

## 🔧 Cấu hình nâng cao

### Configuration

Hệ thống sử dụng **một file config duy nhất** với environment variables để hoạt động cho cả development và production.

#### Development
Chỉnh sửa file `.env`:

```env
# Database Configuration
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=visiobox
REDIS_HOST=localhost
REDIS_PORT=6379

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Application Configuration
NODE_ENV=development
PORT=3001
SECRET_KEY=your_jwt_secret_key
```

#### Production (Docker)
Environment variables được định nghĩa trong `docker-compose.yml` và sẽ tự động override các giá trị development.
```

### Giới hạn file

- Kích thước tối đa: 50MB (giới hạn của Telegram Bot API)
- Loại file hỗ trợ: Ảnh và video
- Số file upload cùng lúc: 10 files

## 🚀 Roadmap

### Giai đoạn 2
- [ ] Tích hợp AI (Gemini) để tự động tag và mô tả ảnh
- [ ] Local Bot API Server để hỗ trợ file > 50MB
- [ ] Xác thực người dùng (JWT)
- [ ] Chia sẻ file/folder

### Giai đoạn 3
- [ ] Ứng dụng mobile React Native
- [ ] Đồng bộ tự động từ điện thoại
- [ ] Backup và restore
- [ ] Quản lý đa người dùng

## 🐛 Troubleshooting

### Lỗi thường gặp

1. **Telegram Bot không hoạt động**
   - Kiểm tra Bot Token và Chat ID
   - Đảm bảo bot đã được thêm vào chat/channel

2. **Upload file thất bại**
   - Kiểm tra kích thước file (< 50MB)
   - Kiểm tra loại file (chỉ ảnh và video)

3. **Database connection error**
   - Kiểm tra MongoDB và Redis đang chạy
   - Kiểm tra connection string trong config

### Debug mode

```bash
# Backend logs
docker-compose logs backend

# Database logs
docker-compose logs mongodb

# Redis logs
docker-compose logs redis
```

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📞 Hỗ trợ

Nếu bạn gặp vấn đề hoặc có câu hỏi, vui lòng tạo issue trên GitHub repository.
